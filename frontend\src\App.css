.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotateY-180 {
  transform: rotateY(180deg);
}

.transform-style-preserve {
  transform-style: preserve-3d;
}

.rotateY-180 {
  transform: rotateY(180deg);
}

.backface-hidden {
  backface-visibility: hidden;
}


@keyframes flip-slide-left {
  0% {
      transform: translateX(100%) rotateY(0deg) opacity(1);
  }
  50% {
      transform: translateX(-50%) rotateY(90deg) opacity(0);
  }
  100% {
      transform: translateX(-100%) rotateY(180deg) opacity(1);
  }
}

@keyframes flip-slide-right {
  0% {
      transform: translateX(-100%) rotateY(0deg) opacity(1);
  }
  50% {
      transform: translateX(50%) rotateY(90deg) opacity(0);
  }
  100% {
      transform: translateX(100%) rotateY(180deg) opacity(1);
  }

  
}

