{"name": "my-app", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@react-three/fiber": "^8.17.12", "@splinetool/react-spline": "^4.0.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@tsparticles/all": "^3.7.1", "@tsparticles/react": "^3.0.0", "animate.css": "^4.1.1", "canvas-confetti": "^1.9.3", "cloudinary": "^2.5.1", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "file-saver": "^2.0.5", "framer-motion": "^11.18.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.487.0", "mammoth": "^1.8.0", "multer": "^1.4.5-lts.1", "pdfjs-dist": "^3.11.174", "react": "^18.3.1", "react-csv": "^2.2.2", "react-dom": "^18.3.1", "react-file-base64": "^1.0.3", "react-icons": "^5.4.0", "react-loader-spinner": "^6.1.6", "react-markdown": "^8.0.7", "react-pdf": "^9.1.1", "react-responsive": "^10.0.1", "react-router-dom": ">=7.5.2", "react-scripts": "^5.0.1", "react-slick": "^0.30.3", "react-tilt": "^1.0.2", "react-toastify": "^11.0.3", "react-tsparticles": "^2.12.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "sweetalert2": "^11.16.0", "sweetalert2-react-content": "^5.1.0", "swiper": "^11.2.6", "three": "^0.172.0", "tsparticles": "^3.7.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "cypress": "^13.17.0", "tailwindcss": "^3.4.17"}}