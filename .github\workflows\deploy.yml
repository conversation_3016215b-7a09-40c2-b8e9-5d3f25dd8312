name: CD Workflow

on:
  pull_request:
    types:
      - closed
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      # Step 1: Checkout the latest code from the repository.
      - name: Checkout repository
        uses: actions/checkout@v3

      # Step 2: Connect to your remote server via SSH and run deployment commands.
      - name: Deploy to Server via SSH
        uses: appleboy/ssh-action@v0.1.6
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            # Update package lists and install curl (if not already installed)
            echo "🔧 Updating system and installing curl..."
            sudo apt-get update -y && sudo apt-get install -y curl

            # Install 'n' to manage Node.js versions globally
            echo "📦 Installing n (Node version manager)..."
            sudo npm install -g n

            # Install the latest stable Node.js version
            echo "⬆️ Installing latest stable Node.js..."
            sudo n stable

            # Verify the installed Node.js version
            echo "✅ Verifying Node.js version..."
            node -v
            npm -v

            # Install PM2 globally to manage Node processes
            echo "🔨 Installing PM2 globally..."
            sudo npm install -g pm2

            # Install 'serve' globally for serving frontend builds
            echo "🔨 Installing serve globally..."
            sudo npm install -g serve

            # Verify PM2 installation
            echo "✅ Verifying PM2 version..."
            pm2 -v

            # Navigate to your project directory – update the path accordingly.
            echo "🚀 Navigating to project directory..."
            cd ~/CareerAgent

            # Checkout the main branch and pull the latest changes
            echo "📂 Checking out main branch and pulling latest changes..."
            git checkout main
            git pull

            # Install backend dependencies and start backend with PM2.
            echo "📦 Installing backend dependencies..."
            cd backend
            npm install

            # Remove any existing backend process to avoid 'Script already launched' error.
            echo "🧹 Deleting any existing backend PM2 process..."
            pm2 delete backend || true

            echo "🟢 Starting backend with PM2..."
            pm2 start server.js --name "backend" --env production

            # Install frontend dependencies, build and start frontend with PM2.
            echo "📦 Installing frontend dependencies..."
            cd ../frontend
            npm install
            npm run build

            # Remove any existing frontend process before starting a new one.
            echo "🧹 Deleting any existing frontend PM2 process..."
            pm2 delete frontend || true

            echo "🟢 Starting frontend with PM2..."
            pm2 start serve --name "frontend" -- -s build -l 3000
