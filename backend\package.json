{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"test": "jest --runInBand --detectOpenHandles --forceExit", "dev": "nodemon server.js", "start": "node server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@google/generative-ai": "^0.21.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cloudinary": "^2.5.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.16", "pdf-parse": "^1.1.1", "socket.io": "^4.8.1", "streamifier": "^0.1.1"}, "devDependencies": {"jest": "^29.7.0", "mongodb-memory-server": "^10.1.2", "nodemon": "^3.1.9", "supertest": "^7.0.0"}}