
services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3001:3001"
    env_file:
      - ./backend/.env
    volumes:
      - ./backend:/app
      - /app/node_modules # Prevent node_modules from being overwritten
    command: ["nodemon", "server.js"]

  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    working_dir: /app/frontend
    volumes:
      - ./frontend:/app/frontend
    ports:
      - "3000:3000"
    env_file:
      - ./frontend/.env
    command: ["sh", "-c", "npm install && npm start"]
